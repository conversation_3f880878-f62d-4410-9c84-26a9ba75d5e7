#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂末端X轴移动测试
测试机械臂末端沿X轴移动10mm
"""

import sys
import os
import time

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def get_current_cartesian_position(socket_fd):
    """获取当前笛卡尔坐标位置"""
    pos = nrc.VectorDouble()
    for i in range(7):
        pos.append(0.0)
    
    result = nrc.get_current_position(socket_fd, 1, pos)  # 1=笛卡尔坐标
    if result == 0:
        return [pos[i] for i in range(6)]  # 返回X,Y,Z,RX,RY,RZ
    else:
        print(f"获取当前位置失败，错误码: {result}")
        return None

def move_x_axis_10mm(socket_fd):
    """沿X轴移动10mm"""
    print("开始X轴移动10mm测试...")
    
    try:
        # 1. 获取当前位置
        print("步骤 1: 获取当前位置...")
        current_pos = get_current_cartesian_position(socket_fd)
        if current_pos is None:
            return False
        
        print(f"当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
        print(f"当前姿态: RX={current_pos[3]:.3f}, RY={current_pos[4]:.3f}, RZ={current_pos[5]:.3f}")
        
        # 2. 计算目标位置 (X轴+10mm)
        target_pos = current_pos.copy()
        target_pos[0] += 10.0  # X轴增加10mm
        
        print(f"目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
        
        # 3. 创建移动命令
        move_cmd = nrc.MoveCmd()
        
        # 设置目标位置
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)
        
        # 设置运动参数
        move_cmd.coord = 1      # 笛卡尔坐标系
        move_cmd.velocity = 20  # 速度20%
        move_cmd.acc = 50       # 加速度50%
        move_cmd.dec = 50       # 减速度50%
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间(0表示不限制)
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 0    # 用户坐标号
        
        # 4. 执行直线运动
        print("步骤 2: 执行直线运动...")
        result = nrc.robot_movel(socket_fd, move_cmd)
        
        if result != 0:
            print(f"移动命令发送失败，错误码: {result}")
            return False
        
        print("移动命令已发送，等待运动完成...")
        
        # 5. 等待运动完成 - 使用简单的时间等待
        print("等待运动完成...")
        time.sleep(5)  # 等待5秒让运动完成
        
        # 6. 验证最终位置
        print("步骤 3: 验证最终位置...")
        time.sleep(0.5)  # 等待稳定
        final_pos = get_current_cartesian_position(socket_fd)
        
        if final_pos:
            print(f"最终位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")
            
            # 计算实际移动距离
            actual_move = final_pos[0] - current_pos[0]
            print(f"实际X轴移动距离: {actual_move:.3f}mm")
            
            # 检查精度 (允许±0.5mm误差)
            if abs(actual_move - 10.0) <= 0.5:
                print("✅ X轴移动10mm测试成功！")
                return True
            else:
                print(f"❌ 移动精度不符合要求，期望10mm，实际{actual_move:.3f}mm")
                return False
        
        return True
        
    except Exception as e:
        print(f"X轴移动测试失败: {e}")
        return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
    
    try:
        # 获取当前伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        print(f"当前伺服状态: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
        
        if servo_status == 3:  # 已经是运行状态
            print("✅ 机器人已处于运行状态")
            return True
        
        # 需要上电
        print("🔋 机器人需要上电，开始上电流程...")
        
        # 清除错误
        print("步骤 1: 清除错误...")
        nrc.clear_error(socket_fd)
        time.sleep(0.5)
        
        # 根据当前状态执行相应操作
        if servo_status == 0:  # 停止状态
            print("步骤 2: 设置为就绪状态...")
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.5)
        elif servo_status == 3:  # 运行状态，先下电
            print("步骤 2: 先执行下电...")
            nrc.set_servo_poweroff(socket_fd)
            time.sleep(1)
        
        # 执行上电
        print("步骤 3: 执行上电操作...")
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}")
            print("请检查安全回路、示教器模式、急停按钮等")
            return False
        
        time.sleep(2)  # 等待上电完成
        
        # 验证上电结果
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
            return False
            
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\n🔋 开始机器人下电...")
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
    
    try:
        # 获取当前伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        print(f"当前伺服状态: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
        
        # 如果机器人在运行状态，执行下电
        if servo_status == 3:  # 运行状态
            print("执行下电操作...")
            result = nrc.set_servo_poweroff(socket_fd)
            if result != 0:
                print(f"❌ 下电失败！返回码: {result}")
                return False
            
            time.sleep(1)
            
            # 验证下电结果
            servo_status = 0
            result = nrc.get_servo_state(socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                servo_status = result[1]
            
            print(f"下电后状态: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
            print("✅ 机器人下电成功！")
            return True
        else:
            print(f"机器人当前不在运行状态，无需下电")
            return True
            
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def x_axis_move_test():
    """X轴移动测试主函数"""
    print("=" * 60)
    print("INEXBOT机械臂末端X轴移动10mm测试")
    print("=" * 60)
    
    socket_fd = -1
    
    try:
        # 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return False
        
        print(f"✅ 连接成功！Socket ID: {socket_fd}")
        
        # 检查并上电机器人
        print("\n🔍 检查机器人状态并上电...")
        if not robot_power_on_if_needed(socket_fd):
            return False
        
        # 检查运行状态
        print("🔍 检查机器人运行状态...")
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            running_status = result[1]
        
        print(f"机器人运行状态: {running_status}")
        
        print("✅ 机器人状态正常，可以执行移动")
        
        # 执行X轴移动测试
        print("\n" + "=" * 40)
        print("开始X轴移动测试")
        print("=" * 40)
        
        success = move_x_axis_10mm(socket_fd)
        
        # 运动完成后执行下电
        robot_power_off(socket_fd)
        
        if success:
            print("\n✅ X轴移动10mm测试完成！")
            return True
        else:
            print("\n❌ X轴移动10mm测试失败！")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        # 断开连接
        if socket_fd > 0:
            print("\n🔌 正在断开连接...")
            try:
                result = nrc.disconnect_robot(socket_fd)
                print(f"断开连接结果: {result}")
                print("✅ 连接已断开")
            except Exception as e:
                print(f"断开连接时发生错误: {e}")

if __name__ == "__main__":
    print("🚀 开始INEXBOT机械臂X轴移动测试...\n")
    
    success = x_axis_move_test()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 X轴移动测试完成！")
        print("✅ 机械臂末端成功沿X轴移动10mm")
        print("✅ 位置精度符合要求")
        print("✅ 运动控制功能正常")
    else:
        print("\n" + "=" * 60)
        print("❌ X轴移动测试失败")
        print("请检查:")
        print("1. 机械臂是否已上电并处于运行状态")
        print("2. 机械臂是否处于安全位置")
        print("3. X轴移动10mm是否会超出工作空间")
        print("4. 是否有障碍物阻挡运动")
        
    print("=" * 60)





