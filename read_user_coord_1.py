#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂用户坐标系1数据读取
读取并显示用户坐标系1的参数数据
"""

import sys
import os

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc
import time

def robot_initialize_and_power_on(socket_fd):
    """机器人初始化和上电流程"""
    print("开始机器人初始化和上电流程...")
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}

    try:
        # 清除错误
        print("步骤 1: 清除错误...")
        nrc.clear_error(socket_fd)
        time.sleep(0.5)

        # 获取当前伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            current_state = result[1]
        else:
            current_state = servo_status

        print(f"步骤 2: 当前伺服状态为 {current_state} ({servo_names.get(current_state, '未知状态')})")

        # 根据状态执行相应操作
        if current_state == 0:  # 停止状态
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.5)
        elif current_state == 3:  # 运行状态
            nrc.set_servo_poweroff(socket_fd)
            time.sleep(1)

        # 执行上电
        print("步骤 3: 执行上电操作...")
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"上电失败！返回码: {result}")
            print("请检查安全回路、示教器模式、急停按钮等")
            return False

        time.sleep(2)  # 等待上电完成

        # 验证上电结果
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            final_state = result[1]
        else:
            final_state = servo_status

        print(f"步骤 4: 上电后状态为 {final_state} ({servo_names.get(final_state, '未知状态')})")

        if final_state == 3:
            print("✅ 机器人上电成功并进入运行状态！")
            return True
        elif final_state == 1:
            print("⚠️ 机器人处于就绪状态，这是正常的")
            print("✅ 机器人上电成功！")

            # 注意：在INEXBOT机器人上，上电后不要再设置伺服状态
            print("步骤 5: 检查最终状态...")
            time.sleep(1)  # 等待状态稳定

            return True
        else:
            print(f"❌ 上电后状态异常: {final_state}")
            return False

    except Exception as e:
        print(f"初始化和上电流程失败: {e}")
        return False

def robot_go_to_home_position(socket_fd):
    """机器人回到原点位置"""
    print("🏠 机器人回到原点位置...")

    try:
        result = nrc.robot_go_home(socket_fd)
        if result != 0:
            print(f"❌ 回原点失败，错误码: {result}")
            return False

        print("✅ 回原点命令已发送，等待运动完成...")
        time.sleep(5)  # 等待回原点完成
        print("✅ 机器人已回到原点位置")
        return True

    except Exception as e:
        print(f"❌ 回原点过程中发生错误: {e}")
        return False

def read_user_coordinate_1_data(socket_fd):
    """读取用户坐标系1的数据"""
    print("📍 读取用户坐标系1数据...")
    
    try:
        # 创建位置向量来存储坐标系参数
        pos = nrc.VectorDouble()
        for _ in range(7):  # 初始化7个元素
            pos.append(0.0)
        
        # 调用函数获取用户坐标系1的参数
        # userNum = 1 表示用户坐标系1
        result = nrc.get_user_coord_para(socket_fd, 1, pos)
        
        if result == 0:  # 成功
            print("✅ 用户坐标系1参数读取成功！")
            print("\n用户坐标系1参数:")
            print(f"  X偏移量: {pos[0]:8.3f} mm")
            print(f"  Y偏移量: {pos[1]:8.3f} mm") 
            print(f"  Z偏移量: {pos[2]:8.3f} mm")
            print(f"  RX旋转: {pos[3]:8.3f} °")
            print(f"  RY旋转: {pos[4]:8.3f} °")
            print(f"  RZ旋转: {pos[5]:8.3f} °")
            
            return True
        else:
            print(f"❌ 读取用户坐标系1参数失败，错误码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 读取用户坐标系1数据时发生错误: {e}")
        return False

def get_current_user_coord_number(socket_fd):
    """获取当前使用的用户坐标编号"""
    print("🔍 获取当前用户坐标编号...")

    try:
        user_num = 0  # 初始化参数
        result = nrc.get_user_coord_number(socket_fd, user_num)

        # 根据文档说明，从返回列表的第二个元素获取数据
        if isinstance(result, list) and len(result) > 1:
            user_num = result[1]
            print(f"当前使用的用户坐标编号: {user_num}")
            return user_num
        else:
            print(f"❌ 获取用户坐标编号失败，返回值: {result}")
            return None

    except Exception as e:
        print(f"❌ 获取用户坐标编号时发生错误: {e}")
        return None

def get_current_position(socket_fd):
    """获取当前位置（笛卡尔坐标）"""
    try:
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)

        result = nrc.get_current_position(socket_fd, 1, pos)  # 1=笛卡尔坐标
        if result == 0:
            return [pos[i] for i in range(6)]  # 返回X,Y,Z,RX,RY,RZ
        else:
            print(f"❌ 获取当前位置失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位置时发生错误: {e}")
        return None

def check_robot_ready_for_movement(socket_fd):
    """检查机器人是否准备好进行运动"""
    print("🔍 检查机器人状态...")

    try:
        # 检查伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)

        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
            if servo_status not in [1, 3]:  # 1=就绪状态, 3=运行状态
                print(f"❌ 机器人伺服状态异常 (当前: {servo_status})")
                print("💡 请确保机器人已上电")
                return False
            elif servo_status == 1:
                print("✅ 机器人处于就绪状态，可以进行运动")
            else:
                print("✅ 机器人处于运行状态，可以进行运动")

        # 检查运行状态
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)

        if isinstance(result, list) and len(result) > 1:
            running_status = result[1]
            if running_status != 0:  # 0=空闲状态
                print(f"❌ 机器人运行状态异常 (当前: {running_status})")
                return False

        print("✅ 机器人状态正常，可以进行运动")
        return True

    except Exception as e:
        print(f"❌ 检查机器人状态时发生错误: {e}")
        return False

def set_remote_mode(socket_fd):
    """设置机器人为远程模式"""
    print("设置机器人为远程模式...")

    try:
        # 获取当前模式
        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)

        if isinstance(result, list) and len(result) > 1:
            current_mode = result[1]
        else:
            current_mode = mode

        mode_desc = {0: "示教模式", 1: "远程模式", 2: "运行模式"}
        print(f"当前模式: {current_mode} ({mode_desc.get(current_mode, '未知模式')})")

        if current_mode == 1:  # 已经是远程模式
            print("✅ 机器人已处于远程模式")
            return True

        # 设置为远程模式
        result = nrc.set_current_mode(socket_fd, 1)  # 1=远程模式
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            print("💡 请在示教器上手动切换到远程模式")
            return False

        time.sleep(0.5)

        # 验证模式切换
        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)
        if isinstance(result, list) and len(result) > 1:
            new_mode = result[1]
            if new_mode == 1:
                print("✅ 已成功切换到远程模式")
                return True
            else:
                print(f"❌ 模式切换失败，当前模式: {new_mode}")
                return False

        return True

    except Exception as e:
        print(f"❌ 设置远程模式时发生错误: {e}")
        return False

def wait_for_motion_complete(socket_fd, timeout=10):
    """等待机器人运动完成"""
    print("等待机器人运动完成...")

    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 检查运行状态
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)

            if isinstance(result, list) and len(result) > 1:
                status = result[1]
                if status == 0:  # 0=空闲状态，表示运动完成
                    print("✅ 机器人运动完成")
                    return True

            time.sleep(0.2)  # 每200ms检查一次

        except Exception as e:
            print(f"检查运动状态时发生错误: {e}")
            break

    print("⚠️ 等待运动完成超时")
    return False

def check_user_coordinate_validity(socket_fd):
    """检查用户坐标系1是否有效设置"""
    print("🔍 检查用户坐标系1有效性...")

    try:
        # 读取用户坐标系1参数
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)

        result = nrc.get_user_coord_para(socket_fd, 1, pos)
        if result != 0:
            print(f"❌ 无法读取用户坐标系1参数，错误码: {result}")
            return False

        # 检查坐标系参数是否合理
        coord_params = [pos[i] for i in range(6)]
        print(f"用户坐标系1参数: X={coord_params[0]:.3f}, Y={coord_params[1]:.3f}, Z={coord_params[2]:.3f}")
        print(f"                RX={coord_params[3]:.3f}, RY={coord_params[4]:.3f}, RZ={coord_params[5]:.3f}")

        # 检查是否所有参数都为0（未设置）
        if all(abs(param) < 0.001 for param in coord_params):
            print("⚠️ 用户坐标系1参数全为0，可能未正确设置")
            print("💡 建议先在示教器上设置用户坐标系1")
            return False

        print("✅ 用户坐标系1参数有效")
        return True

    except Exception as e:
        print(f"❌ 检查用户坐标系时发生错误: {e}")
        return False

def move_to_user_coord_origin(socket_fd):
    """运动到用户坐标系1下的安全位置"""
    print("运动到用户坐标系1下的安全位置...")

    try:
        # 获取当前位置（基坐标系）
        current_pos = get_current_position(socket_fd)
        if current_pos is None:
            return False

        print(f"当前基坐标系位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")

        # 计算当前位置在用户坐标系1下的坐标
        user_coord_offset = [1106.989, -190.841, 686.906]
        current_in_user = [
            current_pos[0] - user_coord_offset[0],
            current_pos[1] - user_coord_offset[1],
            current_pos[2] - user_coord_offset[2]
        ]

        print(f"当前用户坐标系1位置: X={current_in_user[0]:.3f}, Y={current_in_user[1]:.3f}, Z={current_in_user[2]:.3f}")

        # 保持当前姿态，避免姿态变化导致的问题
        current_orientation = [current_pos[3], current_pos[4], current_pos[5]]

        # 设置一个安全的目标位置：在当前用户坐标系位置基础上，只改变Z轴到一个合理的高度
        safe_z = max(current_in_user[2], 50.0)  # 至少50mm高度
        target_pos = [current_in_user[0], current_in_user[1], safe_z] + current_orientation

        print(f"用户坐标系1安全目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")

        # 计算在基坐标系下的预期位置
        expected_base_pos = [
            target_pos[0] + user_coord_offset[0],
            target_pos[1] + user_coord_offset[1],
            target_pos[2] + user_coord_offset[2]
        ]
        print(f"预期基坐标系位置: X={expected_base_pos[0]:.3f}, Y={expected_base_pos[1]:.3f}, Z={expected_base_pos[2]:.3f}")

        # 创建移动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)

        # 设置运动参数
        move_cmd.coord = 3      # 3=用户坐标系
        move_cmd.velocity = 10  # 降低速度到10%
        move_cmd.acc = 20       # 降低加速度
        move_cmd.dec = 20       # 降低减速度
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间(0表示不限制)
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 1    # 用户坐标系1

        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 运动到用户坐标系安全位置失败，错误码: {result}")
            return False

        print("✅ 运动到用户坐标系安全位置命令已发送，等待完成...")
        wait_for_motion_complete(socket_fd, timeout=20)

        # 验证是否到达
        final_pos = get_current_position(socket_fd)
        if final_pos:
            print(f"最终基坐标系位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")

            # 计算实际移动距离
            move_distance = ((final_pos[0] - current_pos[0])**2 +
                           (final_pos[1] - current_pos[1])**2 +
                           (final_pos[2] - current_pos[2])**2)**0.5

            print(f"实际移动距离: {move_distance:.1f}mm")

            if move_distance > 2.0:  # 移动距离超过2mm
                print("✅ 成功运动到用户坐标系1安全位置！")
                return True
            else:
                print("❌ 机器人没有明显运动")
                return False

        return False

    except Exception as e:
        print(f"❌ 运动到用户坐标系安全位置时发生错误: {e}")
        return False

def check_robot_detailed_status(socket_fd):
    """详细检查机器人状态"""
    print("🔍 详细检查机器人状态...")

    try:
        # 检查伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
        print(f"伺服状态: {servo_status} ({servo_names.get(servo_status, '未知')})")

        # 检查运行状态
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            running_status = result[1]
        running_names = {0: "空闲", 1: "运行中", 2: "暂停", 3: "错误"}
        print(f"运行状态: {running_status} ({running_names.get(running_status, '未知')})")

        # 检查模式
        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)
        if isinstance(result, list) and len(result) > 1:
            mode = result[1]
        mode_names = {0: "示教模式", 1: "远程模式", 2: "运行模式"}
        print(f"当前模式: {mode} ({mode_names.get(mode, '未知')})")

        # 如果机器人处于暂停状态，尝试恢复
        if running_status == 2:  # 暂停状态
            print("⚠️ 机器人处于暂停状态，尝试恢复运行...")
            try:
                result = nrc.robot_continue(socket_fd)
                if result == 0:
                    print("✅ 机器人已恢复运行")
                    time.sleep(1)

                    # 再次检查状态
                    running_status = 0
                    result = nrc.get_robot_running_state(socket_fd, running_status)
                    if isinstance(result, list) and len(result) > 1:
                        new_status = result[1]
                        print(f"恢复后运行状态: {new_status} ({running_names.get(new_status, '未知')})")
                else:
                    print(f"❌ 恢复运行失败，错误码: {result}")
            except Exception as e:
                print(f"❌ 恢复运行时发生错误: {e}")

        return True

    except Exception as e:
        print(f"❌ 检查状态时发生错误: {e}")
        return False

def test_simple_movement(socket_fd):
    """测试简单的关节运动"""
    print("执行简单关节运动测试...")

    # 先详细检查状态
    check_robot_detailed_status(socket_fd)

    try:
        # 尝试使用简单的关节运动测试
        print("尝试使用简单关节运动测试...")

        # 获取当前关节位置
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)

        result = nrc.get_current_position(socket_fd, 0, pos)  # 0=关节坐标
        if result != 0:
            print(f"❌ 获取关节位置失败，错误码: {result}")
            return False

        current_joints = [pos[i] for i in range(6)]
        print(f"当前关节位置: {[f'{j:.2f}°' for j in current_joints]}")

        # 创建一个很小的关节运动：第一个关节移动1度
        target_joints = current_joints.copy()
        target_joints[0] += 1.0  # 第一个关节增加1度

        print(f"目标关节位置: {[f'{j:.2f}°' for j in target_joints]}")

        # 创建关节运动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0  # 0=关节坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for joint_val in target_joints:
            move_cmd.targetPosValue.append(joint_val)

        # 设置运动参数 - 使用很低的速度
        move_cmd.coord = 0      # 0=关节坐标系
        move_cmd.velocity = 5   # 速度5%
        move_cmd.acc = 10       # 加速度10%
        move_cmd.dec = 10       # 减速度10%
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间(0表示不限制)
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 0    # 用户坐标号

        # 执行关节运动
        result = nrc.robot_movej(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 关节运动失败，错误码: {result}")
            return False

        print("✅ 关节运动命令已发送，等待完成...")
        wait_for_motion_complete(socket_fd, timeout=10)

        # 验证运动结果
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)

        result = nrc.get_current_position(socket_fd, 0, pos)
        if result == 0:
            final_joints = [pos[i] for i in range(6)]
            print(f"最终关节位置: {[f'{j:.2f}°' for j in final_joints]}")

            # 检查第一个关节是否移动了
            actual_move = final_joints[0] - current_joints[0]
            print(f"第一关节实际移动: {actual_move:.2f}°")

            if abs(actual_move) > 0.1:  # 只要有移动就算成功
                print("✅ 关节运动测试成功！")
                return True
            else:
                print("❌ 关节运动测试失败，机器人没有运动")
                return False

        return False

    except Exception as e:
        print(f"❌ 点动测试时发生错误: {e}")
        return False

def perform_relative_movement_test(socket_fd):
    """执行简单的相对运动测试"""
    print("执行相对运动测试 - 在当前位置基础上移动10mm...")

    try:
        # 获取当前位置
        current_pos = get_current_position(socket_fd)
        if current_pos is None:
            return False

        print(f"当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
        print(f"当前姿态: RX={current_pos[3]:.3f}, RY={current_pos[4]:.3f}, RZ={current_pos[5]:.3f}")

        # 在当前位置基础上，X轴移动10mm，Y轴移动10mm，Z轴向上移动3mm
        target_pos = [
            current_pos[0] + 10.0,  # X + 10mm
            current_pos[1] + 10.0,  # Y + 10mm
            current_pos[2] + 3.0,   # Z + 3mm
            current_pos[3],         # 保持RX
            current_pos[4],         # 保持RY
            current_pos[5]          # 保持RZ
        ]

        print(f"目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
        print(f"保持姿态: RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")

        # 创建移动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)

        # 使用基坐标系进行运动，避免用户坐标系问题
        move_cmd.coord = 1      # 1=直角坐标系（基坐标系）
        move_cmd.velocity = 10  # 降低速度到10%
        move_cmd.acc = 20       # 降低加速度
        move_cmd.dec = 20       # 降低减速度
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间(0表示不限制)
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 0    # 基坐标系

        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 相对运动失败，错误码: {result}")
            return False

        print("✅ 相对运动命令已发送，等待完成...")
        # 使用智能等待机制
        wait_for_motion_complete(socket_fd, timeout=15)

        # 验证最终位置
        final_pos = get_current_position(socket_fd)
        if final_pos:
            print(f"最终位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")

            # 计算实际移动距离
            actual_move_x = final_pos[0] - current_pos[0]
            actual_move_y = final_pos[1] - current_pos[1]
            actual_move_z = final_pos[2] - current_pos[2]

            print(f"实际移动: X={actual_move_x:.3f}mm, Y={actual_move_y:.3f}mm, Z={actual_move_z:.3f}mm")

            # 检查移动精度
            if (abs(actual_move_x - 10.0) <= 2.0 and
                abs(actual_move_y - 10.0) <= 2.0 and
                abs(actual_move_z - 3.0) <= 2.0):
                print("✅ 相对运动测试成功！")
                return True
            else:
                print("⚠️ 运动精度不够理想，但运动已执行")
                return True

        return True

    except Exception as e:
        print(f"❌ 相对运动测试时发生错误: {e}")
        return False

def perform_safe_relative_movement(socket_fd):
    """执行安全的相对运动测试"""
    print("执行安全的相对运动测试...")

    try:
        # 获取当前位置
        current_pos = get_current_position(socket_fd)
        if current_pos is None:
            return False

        print(f"当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")

        # 在当前位置基础上，只在Z轴向上移动5mm（最安全的运动）
        target_pos = [
            current_pos[0],         # X不变
            current_pos[1],         # Y不变
            current_pos[2] + 5.0,   # Z向上5mm
            current_pos[3],         # 保持RX
            current_pos[4],         # 保持RY
            current_pos[5]          # 保持RZ
        ]

        print(f"目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")

        # 创建移动命令 - 使用基坐标系
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)

        # 使用基坐标系，避免用户坐标系问题
        move_cmd.coord = 1      # 1=基坐标系
        move_cmd.velocity = 5   # 很低的速度5%
        move_cmd.acc = 10       # 低加速度
        move_cmd.dec = 10       # 低减速度
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 0    # 基坐标系

        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 安全运动失败，错误码: {result}")
            return False

        print("✅ 安全运动命令已发送，等待完成...")
        wait_for_motion_complete(socket_fd, timeout=10)

        # 验证结果
        final_pos = get_current_position(socket_fd)
        if final_pos:
            actual_move_z = final_pos[2] - current_pos[2]
            print(f"Z轴实际移动: {actual_move_z:.3f}mm")

            if abs(actual_move_z) > 1.0:  # 移动超过1mm就算成功
                print("✅ 安全运动测试成功！机器人可以正常运动")
                return True
            else:
                print("❌ 机器人仍然没有运动")
                return False

        return False

    except Exception as e:
        print(f"❌ 安全运动测试时发生错误: {e}")
        return False

def diagnose_and_fix_motion_issues(socket_fd):
    """诊断并尝试修复机器人运动问题"""
    print("🔍 诊断机器人运动问题...")

    try:
        # 1. 检查伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
        print(f"伺服状态: {servo_status} ({servo_names.get(servo_status, '未知')})")

        # 2. 检查运行状态
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            running_status = result[1]
        running_names = {0: "空闲", 1: "运行中", 2: "暂停", 3: "错误"}
        print(f"运行状态: {running_status} ({running_names.get(running_status, '未知')})")

        # 3. 检查模式
        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)
        if isinstance(result, list) and len(result) > 1:
            mode = result[1]
        mode_names = {0: "示教模式", 1: "远程模式", 2: "运行模式"}
        print(f"当前模式: {mode} ({mode_names.get(mode, '未知')})")

        # 4. 尝试解决问题
        issues_fixed = 0

        # 如果处于暂停状态，尝试恢复
        if running_status == 2:
            print("⚠️ 机器人处于暂停状态，尝试恢复...")
            try:
                result = nrc.job_continue(socket_fd)
                if result == 0:
                    print("✅ 已恢复运行状态")
                    issues_fixed += 1
                    time.sleep(1)
                else:
                    print(f"❌ 恢复运行失败，错误码: {result}")
            except Exception as e:
                print(f"❌ 恢复运行异常: {e}")

        # 如果不在远程模式，尝试切换
        if mode != 1:
            print("⚠️ 机器人不在远程模式，尝试切换...")
            try:
                result = nrc.set_current_mode(socket_fd, 1)
                if result == 0:
                    print("✅ 已切换到远程模式")
                    issues_fixed += 1
                    time.sleep(0.5)
                else:
                    print(f"❌ 切换远程模式失败，错误码: {result}")
            except Exception as e:
                print(f"❌ 切换模式异常: {e}")

        # 尝试清除错误
        print("🔧 尝试清除可能的错误...")
        try:
            nrc.clear_error(socket_fd)
            print("✅ 已清除错误")
            time.sleep(0.5)
        except Exception as e:
            print(f"❌ 清除错误异常: {e}")

        # 注意：不要在远程模式下设置伺服状态，这会导致机器人停止
        print("🔧 检查运动使能状态...")
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            current_servo = result[1]
            if current_servo in [1, 3]:
                print(f"✅ 伺服状态正常: {current_servo} ({servo_names.get(current_servo, '未知')})")
            else:
                print(f"⚠️ 伺服状态异常: {current_servo}，但不尝试修改以避免停机")

        # 最终状态检查
        print("\n📋 最终状态检查:")
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]

        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            running_status = result[1]

        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)
        if isinstance(result, list) and len(result) > 1:
            mode = result[1]

        print(f"伺服状态: {servo_status} ({servo_names.get(servo_status, '未知')})")
        print(f"运行状态: {running_status} ({running_names.get(running_status, '未知')})")
        print(f"当前模式: {mode} ({mode_names.get(mode, '未知')})")

        # 判断是否可以运动
        if servo_status in [1, 3] and running_status in [0, 1] and mode == 1:
            print("✅ 机器人状态正常，应该可以运动")
            return True
        else:
            print("❌ 机器人状态仍然异常")
            print("💡 可能需要在示教器上手动操作：")
            print("   1. 检查急停按钮是否释放")
            print("   2. 检查安全门是否关闭")
            print("   3. 在示教器上启用远程控制")
            print("   4. 检查是否有运动限制设置")
            return False

    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        return False

def test_user_coord_movement(socket_fd):
    """在用户坐标系1下测试简单运动"""
    print("在用户坐标系1下测试运动...")

    try:
        # 获取当前位置（基坐标系）
        current_pos = get_current_position(socket_fd)
        if current_pos is None:
            return False

        print(f"当前基坐标系位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")

        # 计算当前位置在用户坐标系1下的坐标
        # 用户坐标系1: X=1106.989, Y=-190.841, Z=686.906, RY=-1.571
        user_coord_offset = [1106.989, -190.841, 686.906]

        # 当前位置相对于用户坐标系1原点的位置
        current_in_user = [
            current_pos[0] - user_coord_offset[0],
            current_pos[1] - user_coord_offset[1],
            current_pos[2] - user_coord_offset[2]
        ]

        print(f"当前用户坐标系1位置: X={current_in_user[0]:.3f}, Y={current_in_user[1]:.3f}, Z={current_in_user[2]:.3f}")

        # 设置一个安全的目标位置：在当前位置基础上，X轴移动10mm
        target_in_user = [
            current_in_user[0] + 10.0,  # X轴移动10mm
            current_in_user[1],         # Y不变
            current_in_user[2],         # Z不变
            current_pos[3],             # 保持姿态
            current_pos[4],
            current_pos[5]
        ]

        print(f"用户坐标系1目标位置: X={target_in_user[0]:.3f}, Y={target_in_user[1]:.3f}, Z={target_in_user[2]:.3f}")

        # 计算目标位置在基坐标系下的坐标（用于验证）
        target_in_base = [
            target_in_user[0] + user_coord_offset[0],
            target_in_user[1] + user_coord_offset[1],
            target_in_user[2] + user_coord_offset[2]
        ]
        print(f"预期基坐标系目标位置: X={target_in_base[0]:.3f}, Y={target_in_base[1]:.3f}, Z={target_in_base[2]:.3f}")

        # 创建移动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_in_user:
            move_cmd.targetPosValue.append(pos_val)

        # 关键：使用用户坐标系
        move_cmd.coord = 3      # 3=用户坐标系
        move_cmd.velocity = 5   # 降低速度到5%
        move_cmd.acc = 10       # 降低加速度
        move_cmd.dec = 10       # 降低减速度
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 1    # 用户坐标系1

        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 用户坐标系运动失败，错误码: {result}")
            return False

        print("✅ 用户坐标系运动命令已发送，等待完成...")
        wait_for_motion_complete(socket_fd, timeout=15)

        # 验证结果
        final_pos = get_current_position(socket_fd)
        if final_pos:
            print(f"最终基坐标系位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")

            # 计算实际移动距离
            move_x = final_pos[0] - current_pos[0]
            move_y = final_pos[1] - current_pos[1]
            move_z = final_pos[2] - current_pos[2]

            print(f"基坐标系实际移动: X={move_x:.3f}mm, Y={move_y:.3f}mm, Z={move_z:.3f}mm")

            # 检查是否有运动
            total_move = abs(move_x) + abs(move_y) + abs(move_z)
            if total_move > 2.0:  # 总移动距离超过2mm
                print("✅ 用户坐标系1运动测试成功！")
                print(f"💡 机器人在用户坐标系1下成功运动了 {total_move:.1f}mm")
                return True
            else:
                print("❌ 机器人在用户坐标系1下没有明显运动")
                print("💡 可能原因：目标位置不可达或坐标系转换有问题")
                return False

        return False

    except Exception as e:
        print(f"❌ 用户坐标系运动测试时发生错误: {e}")
        return False

def test_base_coord_movement(socket_fd):
    """在基坐标系下测试简单运动作为备用方案"""
    print("在基坐标系下测试运动...")

    try:
        # 获取当前位置
        current_pos = get_current_position(socket_fd)
        if current_pos is None:
            return False

        print(f"当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")

        # 在当前位置基础上，Z轴向上移动5mm（最安全的运动）
        target_pos = [
            current_pos[0],         # X不变
            current_pos[1],         # Y不变
            current_pos[2] + 5.0,   # Z向上5mm
            current_pos[3],         # 保持姿态
            current_pos[4],
            current_pos[5]
        ]

        print(f"基坐标系目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")

        # 创建移动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)

        # 使用基坐标系
        move_cmd.coord = 1      # 1=基坐标系
        move_cmd.velocity = 20   # 很低的速度
        move_cmd.acc = 10       # 低加速度
        move_cmd.dec = 10       # 低减速度
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 0    # 基坐标系

        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 基坐标系运动失败，错误码: {result}")
            return False

        print("✅ 基坐标系运动命令已发送，等待完成...")
        wait_for_motion_complete(socket_fd, timeout=10)

        # 验证结果
        final_pos = get_current_position(socket_fd)
        if final_pos:
            move_z = final_pos[2] - current_pos[2]
            print(f"Z轴实际移动: {move_z:.3f}mm")

            if abs(move_z) > 1.0:
                print("✅ 基坐标系运动测试成功！机器人可以正常运动")
                print("💡 问题可能在于用户坐标系的目标位置不可达")
                return True
            else:
                print("❌ 基坐标系运动也失败")
                print("🔧 最后尝试：使用点动命令...")
                return test_jogging_movement(socket_fd)

        return False

    except Exception as e:
        print(f"❌ 基坐标系运动测试时发生错误: {e}")
        return False

def test_jogging_movement(socket_fd):
    """使用点动命令测试机器人运动"""
    print("使用点动命令测试机器人运动...")

    try:
        # 获取初始位置
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)

        result = nrc.get_current_position(socket_fd, 0, pos)  # 0=关节坐标
        if result != 0:
            print(f"❌ 获取关节位置失败，错误码: {result}")
            return False

        initial_joints = [pos[i] for i in range(6)]
        print(f"初始关节位置: {[f'{j:.2f}°' for j in initial_joints]}")

        # 尝试点动第一个关节
        print("🔧 尝试点动第一个关节正方向...")
        try:
            result = nrc.robot_start_jogging(socket_fd, 0, True)  # 关节0，正方向(True)
            if result != 0:
                print(f"❌ 启动点动失败，错误码: {result}")
                return False

            print("✅ 点动命令已发送，运行2秒...")
            time.sleep(2)

            # 停止点动
            result = nrc.robot_stop_jogging(socket_fd, 0)
            if result != 0:
                print(f"⚠️ 停止点动失败，错误码: {result}")
            else:
                print("✅ 点动已停止")

            time.sleep(1)

            # 检查是否有运动
            pos = nrc.VectorDouble()
            for _ in range(7):
                pos.append(0.0)

            result = nrc.get_current_position(socket_fd, 0, pos)
            if result == 0:
                final_joints = [pos[i] for i in range(6)]
                print(f"最终关节位置: {[f'{j:.2f}°' for j in final_joints]}")

                # 检查第一个关节是否移动
                joint_move = abs(final_joints[0] - initial_joints[0])
                print(f"第一关节移动: {joint_move:.2f}°")

                if joint_move > 0.1:
                    print("✅ 点动测试成功！机器人可以运动")
                    print("💡 问题可能在于复杂运动命令的参数设置")
                    return True
                else:
                    print("❌ 点动测试失败，机器人完全不运动")
                    print("💡 可能的原因：")
                    print("   1. 示教器上禁用了远程运动")
                    print("   2. 安全回路未完全释放")
                    print("   3. 机器人处于维护模式")
                    print("   4. 需要在示教器上手动启用运动权限")
                    return False

            return False

        except Exception as e:
            print(f"❌ 点动测试异常: {e}")
            return False

    except Exception as e:
        print(f"❌ 点动测试时发生错误: {e}")
        return False

def execute_user_coord_relative_movement(socket_fd):
    """在用户坐标系1下执行安全的相对运动"""
    print("在用户坐标系1下执行安全的相对运动...")

    try:
        # 获取当前位置
        current_pos = get_current_position(socket_fd)
        if current_pos is None:
            return False

        print(f"当前基坐标系位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")

        # 计算当前位置在用户坐标系1下的坐标
        user_coord_offset = [1106.989, -190.841, 686.906]
        current_in_user = [
            current_pos[0] - user_coord_offset[0],
            current_pos[1] - user_coord_offset[1],
            current_pos[2] - user_coord_offset[2]
        ]

        print(f"当前用户坐标系1位置: X={current_in_user[0]:.3f}, Y={current_in_user[1]:.3f}, Z={current_in_user[2]:.3f}")

        # 在当前位置基础上进行小幅度的相对运动
        # X轴移动10mm，Y轴移动10mm，Z轴向上移动3mm
        target_pos = [
            current_in_user[0] + 10.0,  # X + 10mm
            current_in_user[1] + 10.0,  # Y + 10mm
            current_in_user[2] + 3.0,   # Z + 3mm
            current_pos[3],             # 保持姿态
            current_pos[4],
            current_pos[5]
        ]

        print(f"用户坐标系1目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")

        # 计算在基坐标系下的预期位置
        expected_base_pos = [
            target_pos[0] + user_coord_offset[0],
            target_pos[1] + user_coord_offset[1],
            target_pos[2] + user_coord_offset[2]
        ]
        print(f"预期基坐标系位置: X={expected_base_pos[0]:.3f}, Y={expected_base_pos[1]:.3f}, Z={expected_base_pos[2]:.3f}")

        # 创建移动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)

        # 设置运动参数
        move_cmd.coord = 3      # 3=用户坐标系
        move_cmd.velocity = 8   # 降低速度到8%
        move_cmd.acc = 15       # 降低加速度
        move_cmd.dec = 15       # 降低减速度
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间(0表示不限制)
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = 1    # 用户坐标系1

        # 执行运动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 用户坐标系相对运动失败，错误码: {result}")
            return False

        print("✅ 用户坐标系相对运动命令已发送，等待完成...")
        wait_for_motion_complete(socket_fd, timeout=25)

        # 验证结果
        final_pos = get_current_position(socket_fd)
        if final_pos:
            print(f"最终基坐标系位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")

            # 计算实际移动距离
            move_x = final_pos[0] - current_pos[0]
            move_y = final_pos[1] - current_pos[1]
            move_z = final_pos[2] - current_pos[2]
            total_move = (move_x**2 + move_y**2 + move_z**2)**0.5

            print(f"基坐标系实际移动: X={move_x:.3f}mm, Y={move_y:.3f}mm, Z={move_z:.3f}mm")
            print(f"总移动距离: {total_move:.1f}mm")

            if total_move > 5.0:  # 总移动距离超过5mm
                print("✅ 用户坐标系1相对运动成功！")
                print(f"💡 机器人在用户坐标系1下成功运动了 {total_move:.1f}mm")
                print(f"💡 这证明了机器人可以在用户坐标系1下正确运动")
                return True
            else:
                print("❌ 机器人在用户坐标系1下没有明显运动")
                return False

        return False

    except Exception as e:
        print(f"❌ 用户坐标系相对运动时发生错误: {e}")
        return False

def move_to_position_in_user_coord_1(socket_fd):
    """完整流程：恢复原点 → 运动到用户坐标系原点 → 相对运动"""
    print("\n🎯 完整的用户坐标系1运动流程...")

    try:
        # 0. 检查机器人状态
        if not check_robot_ready_for_movement(socket_fd):
            return False

        # 1. 恢复机器人原点
        print("步骤 1: 恢复机器人原点...")
        if not robot_go_to_home_position(socket_fd):
            print("❌ 恢复原点失败")
            return False

        # 2. 设置当前坐标系为用户坐标系
        print("步骤 2: 设置当前坐标系为用户坐标系...")
        result = nrc.set_current_coord(socket_fd, 3)  # 3=用户坐标系
        if result != 0:
            print(f"❌ 设置坐标系失败，错误码: {result}")
            return False
        print("✅ 已设置为用户坐标系")

        # 3. 切换到用户坐标系1
        print("步骤 3: 切换到用户坐标系1...")
        result = nrc.set_user_coord_number(socket_fd, 1)
        if result != 0:
            print(f"❌ 切换到用户坐标系1失败，错误码: {result}")
            return False
        print("✅ 已切换到用户坐标系1")

        # 4. 运动到用户坐标系1的原点
        print("步骤 4: 运动到用户坐标系1原点...")
        if not move_to_user_coord_origin(socket_fd):
            print("❌ 运动到用户坐标系原点失败")
            return False

        # 5. 在用户坐标系1下执行相对运动
        print("步骤 5: 在用户坐标系1下执行相对运动到(10,10,3)...")
        return execute_user_coord_relative_movement(socket_fd)

    except Exception as e:
        print(f"❌ 运动过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("INEXBOT机械臂用户坐标系1数据读取")
    print("=" * 60)
    print("💡 使用说明：")
    print("   1. 确保示教器处于示教模式")
    print("   2. 运行程序进行上电和测试")
    print("   3. 程序会自动切换到远程模式进行运动测试")
    print("=" * 60)
    
    socket_fd = -1
    
    try:
        # 1. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return False
        
        print(f"✅ 连接成功！Socket ID: {socket_fd}")
        
        # 2. 检查连接状态
        print("\n📡 检查连接状态...")
        status = nrc.get_connection_status(socket_fd)
        if status != 0:
            print(f"❌ 连接状态异常: {status}")
            return False
        print("✅ 连接状态正常")

        # 3. 机器人上电
        print("\n🔋 机器人上电...")
        if not robot_initialize_and_power_on(socket_fd):
            return False

        # 3.5. 设置为远程模式
        print("\n🎮 设置为远程模式...")
        if not set_remote_mode(socket_fd):
            return False

        # 4. 获取当前用户坐标编号
        current_user_coord = get_current_user_coord_number(socket_fd)

        # 5. 读取用户坐标系1的数据
        coord_read_success = read_user_coordinate_1_data(socket_fd)

        if coord_read_success:
            print("\n✅ 用户坐标系1数据读取完成！")
            if current_user_coord == 1:
                print("💡 当前正在使用用户坐标系1")
            else:
                print(f"💡 当前使用的是用户坐标系{current_user_coord}，不是坐标系1")
        else:
            print("\n❌ 用户坐标系1数据读取失败！")
            return False

        # 6. 在用户坐标系1下运动到指定位置
        print("\n" + "="*50)
        print("开始运动测试...")
        move_success = move_to_position_in_user_coord_1(socket_fd)

        if move_success:
            print("\n✅ 所有操作完成成功！")
        else:
            print("\n❌ 运动操作失败！")

        return coord_read_success and move_success
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误: {e}")
        return False
        
    finally:
        # 断开连接
        if socket_fd > 0:
            print("\n🔌 正在断开连接...")
            try:
                nrc.disconnect_robot(socket_fd)
                print("✅ 连接已断开")
            except Exception as e:
                print(f"断开连接时发生错误: {e}")

if __name__ == "__main__":
    main()
